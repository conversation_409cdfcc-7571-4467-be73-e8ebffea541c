import apiClient from "@/apiClient";
/**
 * Custom Fields Processor
 * 
 * Simple processor that returns empty JSON response.
 */

export async function synchronizeCustomFields(): Promise<{}> {
	const apCustomFields = await apiClient.ap.apCustomfield.allWithParentFilter();
	const ccCustomFields = await apiClient.cc.ccCustomfieldReq.all();

	const matchFields = []

	ccCustomFields.forEach((ccf) => {
		console.log(ccf);
	});

	return {
		apCustomFields,
		ccCustomFields,
	};
}
