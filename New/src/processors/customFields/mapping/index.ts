import type { GetCCCustomField } from "@/type/CCTypes";

/**
 * AutoPatient field types as defined in the DATA-TYPE-MAP.md
 */
export type APFieldType =
	| "TEXT"
	| "LARGE_TEXT"
	| "NUMERICAL"
	| "PHONE"
	| "MONETORY"
	| "CHECKBOX"
	| "SINGLE_OPTIONS"
	| "MULTIPLE_OPTIONS"
	| "DATE"
	| "RADIO"
	| "EMAIL"
	| "TEXTBOX_LIST"
	| "FILE_UPLOAD";

/**
 * CliniCore field types as defined in the DATA-TYPE-MAP.md
 */
export type CCFieldType =
	| "text"
	| "textarea"
	| "number"
	| "telephone"
	| "email"
	| "date"
	| "select"
	| "select-or-custom"
	| "boolean"
	| "medication"
	| "permanent-diagnoses"
	| "patient-has-recommended";

/**
 * Converts a CliniCore (CC) custom field type to the corresponding AutoPatient (AP) field type.
 *
 * This function implements the field type mapping rules defined in DATA-TYPE-MAP.md:
 *
 * **Key Rules:**
 * 1. CC fields with `allowMultipleValues=true` map to AP `TEXTBOX_LIST` regardless of base type,
 *    EXCEPT for CC `select` fields which prefer `MULTIPLE_OPTIONS` for semantic accuracy
 * 2. Unknown CC field types default to AP `TEXT` (or `TEXTBOX_LIST` if `allowMultipleValues=true`)
 * 3. Medical field types (`medication`, `permanent-diagnoses`, `patient-has-recommended`) fallback to `TEXT`
 * 4. CC `boolean` fields map to AP `RADIO` with Yes/Ja, No/Nein options
 *
 * **Multi-Value Handling Priority:**
 * - CC `select` with `allowMultipleValues=true` → AP `MULTIPLE_OPTIONS` (preferred for semantic accuracy)
 * - All other CC field types with `allowMultipleValues=true` → AP `TEXTBOX_LIST`
 *
 * @param ccField - CliniCore custom field object containing type and allowMultipleValues properties
 * @returns The corresponding AutoPatient field type
 *
 * @example
 * ```typescript
 * // Basic field type mapping
 * const textField = { type: "text", allowMultipleValues: false };
 * mapCCFieldToAPType(textField); // Returns "TEXT"
 *
 * // Multi-value field mapping
 * const multiTextField = { type: "text", allowMultipleValues: true };
 * mapCCFieldToAPType(multiTextField); // Returns "TEXTBOX_LIST"
 *
 * // Select field with multi-value (preferred semantic mapping)
 * const multiSelectField = { type: "select", allowMultipleValues: true };
 * mapCCFieldToAPType(multiSelectField); // Returns "MULTIPLE_OPTIONS"
 *
 * // Boolean field mapping
 * const booleanField = { type: "boolean", allowMultipleValues: false };
 * mapCCFieldToAPType(booleanField); // Returns "RADIO"
 *
 * // Medical field fallback
 * const medicationField = { type: "medication", allowMultipleValues: false };
 * mapCCFieldToAPType(medicationField); // Returns "TEXT"
 *
 * // Unknown field type fallback
 * const unknownField = { type: "unknown-type", allowMultipleValues: false };
 * mapCCFieldToAPType(unknownField); // Returns "TEXT"
 * ```
 */
export function mapCCFieldToAPType(ccField: Pick<GetCCCustomField, "type" | "allowMultipleValues">): APFieldType {
	const { type, allowMultipleValues } = ccField;

	// Handle multi-value fields first (priority-based system)
	if (allowMultipleValues) {
		// CC select fields with allowMultipleValues → AP MULTIPLE_OPTIONS (preferred for semantic accuracy)
		if (type === "select" || type === "select-or-custom") {
			return "MULTIPLE_OPTIONS";
		}

		// All other CC field types with allowMultipleValues → AP TEXTBOX_LIST
		return "TEXTBOX_LIST";
	}

	// Handle single-value field mappings based on DATA-TYPE-MAP.md
	switch (type) {
		// Basic field type mappings
		case "text":
			return "TEXT";
		case "textarea":
			return "LARGE_TEXT";
		case "number":
			return "NUMERICAL";
		case "telephone":
			return "PHONE";
		case "email":
			return "EMAIL";
		case "date":
			return "DATE";

		// Select field mappings
		case "select":
		case "select-or-custom":
			return "SINGLE_OPTIONS";

		// Boolean field mapping (creates RADIO with Yes/Ja, No/Nein options)
		case "boolean":
			return "RADIO";

		// Medical field fallbacks (as defined in DATA-TYPE-MAP.md)
		case "medication":
		case "permanent-diagnoses":
		case "patient-has-recommended":
			return "TEXT";

		// Unknown field type fallback
		default:
			// Log unknown field type for debugging
			console.warn(`Unknown CC field type "${type}", defaulting to TEXT`);
			return "TEXT";
	}
}