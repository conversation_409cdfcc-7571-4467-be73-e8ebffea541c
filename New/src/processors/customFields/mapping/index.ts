import type { GetCCCustomField } from "@/type/CCTypes";

/**
 * AutoPatient field types as defined in the DATA-TYPE-MAP.md
 */
export type APFieldType =
	| "TEXT"
	| "LARGE_TEXT"
	| "NUMERICAL"
	| "PHONE"
	| "MONETORY"
	| "CHECKBOX"
	| "SINGLE_OPTIONS"
	| "MULTIPLE_OPTIONS"
	| "DATE"
	| "RADIO"
	| "EMAIL"
	| "TEXTBOX_LIST"
	| "FILE_UPLOAD";

/**
 * CliniCore field types as defined in the DATA-TYPE-MAP.md
 */
export type CCFieldType =
	| "text"
	| "textarea"
	| "number"
	| "telephone"
	| "email"
	| "date"
	| "select"
	| "select-or-custom"
	| "boolean"
	| "medication"
	| "permanent-diagnoses"
	| "patient-has-recommended";

/**
 * Converts a CliniCore (CC) custom field type to the corresponding AutoPatient (AP) field type.
 *
 * This function implements the field type mapping rules defined in DATA-TYPE-MAP.md:
 *
 * **Key Rules:**
 * 1. CC fields with `allowMultipleValues=true` map to AP `TEXTBOX_LIST` regardless of base type,
 *    EXCEPT for CC `select` fields which prefer `MULTIPLE_OPTIONS` for semantic accuracy
 * 2. Unknown CC field types default to AP `TEXT` (or `TEXTBOX_LIST` if `allowMultipleValues=true`)
 * 3. Medical field types (`medication`, `permanent-diagnoses`, `patient-has-recommended`) fallback to `TEXT`
 * 4. CC `boolean` fields map to AP `RADIO` with Yes/Ja, No/Nein options
 *
 * **Multi-Value Handling Priority:**
 * - CC `select` with `allowMultipleValues=true` → AP `MULTIPLE_OPTIONS` (preferred for semantic accuracy)
 * - All other CC field types with `allowMultipleValues=true` → AP `TEXTBOX_LIST`
 *
 * @param ccField - CliniCore custom field object containing type and allowMultipleValues properties
 * @returns The corresponding AutoPatient field type
 *
 * @example
 * ```typescript
 * // Basic field type mapping
 * const textField = { type: "text", allowMultipleValues: false };
 * mapCCFieldToAPType(textField); // Returns "TEXT"
 *
 * // Multi-value field mapping
 * const multiTextField = { type: "text", allowMultipleValues: true };
 * mapCCFieldToAPType(multiTextField); // Returns "TEXTBOX_LIST"
 *
 * // Select field with multi-value (preferred semantic mapping)
 * const multiSelectField = { type: "select", allowMultipleValues: true };
 * mapCCFieldToAPType(multiSelectField); // Returns "MULTIPLE_OPTIONS"
 *
 * // Boolean field mapping
 * const booleanField = { type: "boolean", allowMultipleValues: false };
 * mapCCFieldToAPType(booleanField); // Returns "RADIO"
 *
 * // Medical field fallback
 * const medicationField = { type: "medication", allowMultipleValues: false };
 * mapCCFieldToAPType(medicationField); // Returns "TEXT"
 *
 * // Unknown field type fallback
 * const unknownField = { type: "unknown-type", allowMultipleValues: false };
 * mapCCFieldToAPType(unknownField); // Returns "TEXT"
 * ```
 */
export function mapCCFieldToAPType(ccField: Pick<GetCCCustomField, "type" | "allowMultipleValues">): APFieldType {
	const { type, allowMultipleValues } = ccField;

	// Handle multi-value fields first (priority-based system)
	if (allowMultipleValues) {
		// CC select fields with allowMultipleValues → AP MULTIPLE_OPTIONS (preferred for semantic accuracy)
		if (type === "select" || type === "select-or-custom") {
			return "MULTIPLE_OPTIONS";
		}

		// All other CC field types with allowMultipleValues → AP TEXTBOX_LIST
		return "TEXTBOX_LIST";
	}

	// Handle single-value field mappings based on DATA-TYPE-MAP.md
	switch (type) {
		// Basic field type mappings
		case "text":
			return "TEXT";
		case "textarea":
			return "LARGE_TEXT";
		case "number":
			return "NUMERICAL";
		case "telephone":
			return "PHONE";
		case "email":
			return "EMAIL";
		case "date":
			return "DATE";

		// Select field mappings
		case "select":
		case "select-or-custom":
			return "SINGLE_OPTIONS";

		// Boolean field mapping (creates RADIO with Yes/Ja, No/Nein options)
		case "boolean":
			return "RADIO";

		// Medical field fallbacks (as defined in DATA-TYPE-MAP.md)
		case "medication":
		case "permanent-diagnoses":
		case "patient-has-recommended":
			return "TEXT";

		// Unknown field type fallback
		default:
			console.warn(`Unknown CC field type "${type}", defaulting to TEXT`);
			return allowMultipleValues ? "TEXTBOX_LIST" : "TEXT";
	}
}

/**
 * Result of AP to CC field type conversion
 */
export interface APToCCConversionResult {
	/** The CC field type */
	type: CCFieldType | null;
	/** Whether the CC field should allow multiple values */
	allowMultipleValues: boolean;
}

/**
 * Converts an AutoPatient (AP) field type to the corresponding CliniCore (CC) field type.
 *
 * This function implements the reverse field type mapping rules defined in DATA-TYPE-MAP.md:
 *
 * **Key Rules:**
 * 1. AP `TEXTBOX_LIST` maps to CC `text` with `allowMultipleValues=true`
 * 2. AP `MULTIPLE_OPTIONS` and `CHECKBOX` map to CC `select` with `allowMultipleValues=true`
 * 3. AP `SINGLE_OPTIONS` and `RADIO` map to CC `select` with `allowMultipleValues=false`
 * 4. AP `RADIO` with Yes/No options can optionally map to CC `boolean` (context-dependent)
 * 5. AP `MONETORY` maps to CC `text` (monetary values stored as text in CC)
 * 6. AP `FILE_UPLOAD` is skipped (not supported in CC)
 *
 * **Multi-Value Handling:**
 * - AP fields that handle multiple values (`TEXTBOX_LIST`, `MULTIPLE_OPTIONS`, `CHECKBOX`)
 *   map to CC fields with `allowMultipleValues=true`
 * - Single-value AP fields map to CC fields with `allowMultipleValues=false`
 *
 * @param apFieldType - AutoPatient field type to convert
 * @param options - Optional conversion options
 * @param options.preferBoolean - If true, RADIO fields with Yes/No values prefer CC boolean over select
 * @returns Conversion result with CC field type, allowMultipleValues flag, and optional notes
 *
 * @example
 * ```typescript
 * // Basic field type mapping
 * mapAPFieldToCCType("TEXT");
 * // Returns { type: "text", allowMultipleValues: false }
 *
 * // Multi-value field mapping
 * mapAPFieldToCCType("TEXTBOX_LIST");
 * // Returns { type: "text", allowMultipleValues: true }
 *
 * // Select field mappings
 * mapAPFieldToCCType("MULTIPLE_OPTIONS");
 * // Returns { type: "select", allowMultipleValues: true }
 *
 * mapAPFieldToCCType("SINGLE_OPTIONS");
 * // Returns { type: "select", allowMultipleValues: false }
 *
 * // Radio field with boolean preference
 * mapAPFieldToCCType("RADIO", { preferBoolean: true });
 * // Returns { type: "boolean", allowMultipleValues: false, notes: "..." }
 *
 * // Monetary field mapping
 * mapAPFieldToCCType("MONETORY");
 * // Returns { type: "text", allowMultipleValues: false, notes: "..." }
 * ```
 */
export function mapAPFieldToCCType(
	apFieldType: APFieldType
): APToCCConversionResult {

	switch (apFieldType) {
		// Basic field type mappings
		case "TEXT":
			return { type: "text", allowMultipleValues: false };

		case "LARGE_TEXT":
			return { type: "textarea", allowMultipleValues: false };

		case "NUMERICAL":
			return { type: "number", allowMultipleValues: false };

		case "PHONE":
			return { type: "telephone", allowMultipleValues: false };

		case "EMAIL":
			return { type: "email", allowMultipleValues: false };

		case "DATE":
			return { type: "date", allowMultipleValues: false };

		// Multi-value field mappings
		case "TEXTBOX_LIST":
			return {
				type: "text",
				allowMultipleValues: true
			};

		// Select field mappings
		case "SINGLE_OPTIONS":
			return { type: "select", allowMultipleValues: false };

		case "MULTIPLE_OPTIONS":
			return {
				type: "select",
				allowMultipleValues: true
			};

		case "CHECKBOX":
			return {
				type: "select",
				allowMultipleValues: false
			};

		// Radio field mapping (context-dependent)
		case "RADIO":
			return {
					type: "boolean",
					allowMultipleValues: false
				};

		// Special field mappings
		case "MONETORY":
			return {
				type: "number",
				allowMultipleValues: false
			};
		// Unknown field type fallback
		default:
			// Log unknown field type for debugging
			console.warn(`Unknown AP field type "${apFieldType}", defaulting to text`);
			return {
				type: null,
				allowMultipleValues: false
			};
	}
}

/**
 * Utility function to check if an AP field type supports multiple values.
 *
 * @param apFieldType - AutoPatient field type to check
 * @returns True if the field type supports multiple values
 *
 * @example
 * ```typescript
 * isAPFieldMultiValue("TEXTBOX_LIST"); // Returns true
 * isAPFieldMultiValue("MULTIPLE_OPTIONS"); // Returns true
 * isAPFieldMultiValue("TEXT"); // Returns false
 * ```
 */
export function isAPFieldMultiValue(apFieldType: APFieldType): boolean {
	return apFieldType === "TEXTBOX_LIST" ||
		   apFieldType === "MULTIPLE_OPTIONS" ||
		   apFieldType === "CHECKBOX";
}

/**
 * Utility function to check if a CC field supports multiple values based on its configuration.
 *
 * @param ccField - CliniCore custom field object
 * @returns True if the field supports multiple values
 *
 * @example
 * ```typescript
 * const field = { type: "text", allowMultipleValues: true };
 * isCCFieldMultiValue(field); // Returns true
 * ```
 */
export function isCCFieldMultiValue(ccField: Pick<GetCCCustomField, "allowMultipleValues">): boolean {
	return ccField.allowMultipleValues === true;
}

/**
 * Utility function to get both conversion directions for a field type.
 * Useful for validation and compatibility checking.
 *
 * @param ccField - CliniCore custom field object
 * @returns Object containing both conversion directions
 *
 * @example
 * ```typescript
 * const ccField = { type: "text", allowMultipleValues: true };
 * const conversions = getBidirectionalMapping(ccField);
 * // Returns:
 * // {
 * //   ccToAP: "TEXTBOX_LIST",
 * //   apToCC: { type: "text", allowMultipleValues: true, notes: "..." }
 * // }
 * ```
 */
export function getBidirectionalMapping(ccField: Pick<GetCCCustomField, "type" | "allowMultipleValues">): {
	ccToAP: APFieldType;
	apToCC: APToCCConversionResult;
} {
	const ccToAP = mapCCFieldToAPType(ccField);
	const apToCC = mapAPFieldToCCType(ccToAP);

	return {
		ccToAP,
		apToCC
	};
}