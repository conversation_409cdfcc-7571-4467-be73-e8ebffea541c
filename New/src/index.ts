import {
	cfH<PERSON><PERSON>,
	handleErrorCleanup,
} from "@handlers";
import { Hono } from "hono";
import { contextStorage } from "hono/context-storage";
import { requestId } from "hono/request-id";
import { createQueueRoutes } from "@/routes/queueRoutes";
import { logError } from "@/utils/logger";

const app = new Hono<Env>();
app.use(contextStorage());
app.use("*", requestId());

app.onError((err, c) => {
	const requestId = c.get("requestId") || "unknown";
	logError("Unhandled application error", err);
	return c.json(
		{
			message: "Internal Server Error",
			requestId,
			timestamp: new Date().toISOString(),
		},
		500,
	);
});

// Health check endpoints
app.get("/", (c) => c.text("DermaCare Data Sync Service - OK"));
app.get("/health", (c) => c.text("DermaCare Data Sync Service - OK"));

app.get("/cf", cfHandler);

// Webhook endpoints (simplified - return empty JSON)
app.post("/webhooks/ap", (c) => c.json({}));
app.post("/webhooks/cc", (c) => c.json({}));

// Internal webhook endpoints removed - using queue-based processing instead

// Admin endpoints
app.get("/admin/cleanup-errors", handleErrorCleanup);

// Queue system endpoints
app.route("/api/queue", createQueueRoutes());



export default app;
